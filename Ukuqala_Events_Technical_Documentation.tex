\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[english]{babel}
\usepackage{geometry}
\usepackage{graphicx}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{tocloft}
\usepackage{titlesec}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{tikz}
\usepackage{pgfplots}

% Page setup
\geometry{margin=1in}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Ukuqala Events - Technical Documentation}
\fancyhead[R]{\thepage}
\fancyfoot[C]{© 2024 Ukuqala Events Development Team}

% Code listing setup
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,                 
    captionpos=b,                    
    keepspaces=true,                 
    numbers=left,                    
    numbersep=5pt,                  
    showspaces=false,                
    showstringspaces=false,
    showtabs=false,                  
    tabsize=2
}

\lstset{style=mystyle}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,      
    urlcolor=cyan,
    pdftitle={Ukuqala Events Technical Documentation},
    pdfpagemode=FullScreen,
}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{darkblue}}{\thesubsection}{1em}{}

\begin{document}

% Title Page
\begin{titlepage}
    \centering
    \vspace*{2cm}
    
    {\Huge\bfseries Ukuqala Events}\\[0.5cm]
    {\LARGE Online Event Booking System}\\[1cm]
    {\Large Technical Documentation}\\[2cm]
    
    \includegraphics[width=0.3\textwidth]{logo.png}\\[1cm]
    
    {\large Version 1.0.0}\\[0.5cm]
    {\large December 2024}\\[2cm]
    
    \begin{tabular}{ll}
        \textbf{Technology Stack:} & PHP 8.0+, MySQL 8.0, Bootstrap 5, Docker \\
        \textbf{Architecture:} & MVC Pattern with Modular Components \\
        \textbf{Development Team:} & Ukuqala Events Development Team \\
    \end{tabular}
    
    \vfill
    
    {\large Comprehensive Documentation covering Project Overview, Design, Implementation, Deployment, Code Explanation and User Manual}
    
\end{titlepage}

% Table of Contents
\tableofcontents
\newpage

% Executive Summary
\section{Executive Summary}

The Ukuqala Events Online Event Booking System is a comprehensive web-based platform designed to facilitate event management and ticket booking operations. This document provides detailed technical documentation covering all aspects of the system from project overview to deployment and user operations.

\subsection{Key Highlights}
\begin{itemize}
    \item Modern PHP 8.0+ web application with MySQL database
    \item Secure user authentication and role-based access control
    \item Complete event management and booking workflow
    \item Docker-based deployment for easy setup and scalability
    \item Responsive design with modern UI/UX principles
    \item Comprehensive security implementations
\end{itemize}

% Project Overview
\section{Project Overview}

\subsection{System Purpose}
The Ukuqala Events system serves as a centralized platform for event organizers to manage events and for users to discover, book, and manage event tickets. The system addresses the need for a reliable, secure, and user-friendly event booking solution.

\subsection{System Scope}
\begin{itemize}
    \item \textbf{User Management:} Registration, authentication, profile management
    \item \textbf{Event Management:} CRUD operations for events with rich metadata
    \item \textbf{Booking System:} Shopping cart, checkout, payment processing
    \item \textbf{Administrative Functions:} Admin dashboard, reporting, system management
    \item \textbf{Communication:} Email notifications and confirmations
\end{itemize}

\subsection{Technology Stack}

\begin{table}[H]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Layer} & \textbf{Technology} & \textbf{Version} \\
\hline
Frontend & HTML5, CSS3, JavaScript & Latest \\
UI Framework & Bootstrap & 5.x \\
Backend & PHP & 8.0+ \\
Database & MySQL & 8.0 \\
Web Server & Apache & 2.4+ \\
Containerization & Docker & Latest \\
Email & PHPMailer & 6.x \\
\hline
\end{tabular}
\caption{Technology Stack Overview}
\end{table}

\subsection{Key Features}

\subsubsection{Core Functionality}
\begin{enumerate}
    \item \textbf{User Authentication System}
    \begin{itemize}
        \item Secure registration and login
        \item Password hashing with bcrypt
        \item Session management with timeout
        \item Role-based access control (User/Admin)
    \end{itemize}
    
    \item \textbf{Event Management}
    \begin{itemize}
        \item Complete CRUD operations for events
        \item Image upload and management
        \item Category-based organization
        \item Real-time availability tracking
    \end{itemize}
    
    \item \textbf{Booking System}
    \begin{itemize}
        \item Shopping cart functionality
        \item Secure checkout process
        \item Payment processing simulation
        \item Booking confirmation and management
    \end{itemize}
    
    \item \textbf{Administrative Features}
    \begin{itemize}
        \item Comprehensive admin dashboard
        \item Event and user management
        \item Booking oversight and reporting
        \item System analytics and statistics
    \end{itemize}
\end{enumerate}

\subsubsection{Security Features}
\begin{itemize}
    \item CSRF token protection
    \item SQL injection prevention through prepared statements
    \item Input sanitization and validation
    \item Secure password storage
    \item Session security measures
\end{itemize}

% System Architecture and Design
\section{System Architecture and Design}

\subsection{Architectural Pattern}
The system follows a Model-View-Controller (MVC) architectural pattern with clear separation of concerns:

\begin{figure}[H]
\centering
\begin{tikzpicture}[node distance=3cm]
    \node (view) [rectangle, draw, fill=blue!20, text width=3cm, text centered] {Presentation Layer\\(Views/UI)};
    \node (controller) [rectangle, draw, fill=green!20, text width=3cm, text centered, right of=view] {Business Logic\\(Controllers)};
    \node (model) [rectangle, draw, fill=red!20, text width=3cm, text centered, right of=controller] {Data Layer\\(Models/Database)};
    
    \draw [<->] (view) -- (controller);
    \draw [<->] (controller) -- (model);
\end{tikzpicture}
\caption{System Architecture Overview}
\end{figure}

\subsection{Directory Structure}
The application follows a modular directory structure for maintainability and scalability:

\begin{lstlisting}[language=bash, caption=Project Directory Structure]
ukuqala-events/
├── admin/              # Administrative panel
│   ├── index.php       # Admin dashboard
│   ├── events.php      # Event management
│   ├── bookings.php    # Booking management
│   └── reports.php     # Analytics and reports
├── auth/               # Authentication system
│   ├── login.php       # User login
│   ├── register.php    # User registration
│   ├── logout.php      # Session termination
│   └── forgot-password.php # Password recovery
├── booking/            # Booking system
│   ├── cart.php        # Shopping cart
│   ├── checkout.php    # Checkout process
│   ├── confirmation.php # Booking confirmation
│   └── payment-processing.php # Payment handling
├── events/             # Event-related pages
│   ├── index.php       # Event listing
│   ├── details.php     # Event details
│   └── search.php      # Event search
├── user/               # User dashboard
│   ├── dashboard.php   # User home
│   ├── profile.php     # Profile management
│   └── bookings.php    # User bookings
├── includes/           # Core system files
│   ├── config.php      # System configuration
│   ├── functions.php   # Business logic classes
│   └── email.php       # Email functionality
├── assets/             # Static resources
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   └── images/        # Image assets
├── database/           # Database schemas
│   ├── schema.sql     # Database structure
│   └── init-data.sql  # Sample data
└── docker/             # Docker configuration
    └── apache-config.conf # Apache configuration
\end{lstlisting}

\subsection{Core Components}

\subsubsection{Database Layer}
The system utilizes a custom Database class that provides PDO-based database abstraction:

\begin{lstlisting}[language=PHP, caption=Database Connection Class]
class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $dbh;
    private $error;
    private $stmt;

    public function __construct() {
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        );

        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            die('Database connection failed: ' . $this->error);
        }
    }

    public function query($query) {
        $this->stmt = $this->dbh->prepare($query);
    }

    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    public function execute() {
        return $this->stmt->execute();
    }

    public function resultset() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_OBJ);
    }

    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_OBJ);
    }
}
\end{lstlisting}

\subsubsection{Authentication System}
The authentication system provides secure user management with role-based access:

\begin{lstlisting}[language=PHP, caption=User Authentication Class]
class UserManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function register($userData) {
        // Validate input data
        $errors = $this->validateRegistrationData($userData);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // Hash password
        $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

        // Insert user record
        $this->db->query('INSERT INTO users (username, email, password, first_name, last_name, phone, address)
                         VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address)');

        $this->db->bind(':username', $userData['username']);
        $this->db->bind(':email', $userData['email']);
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':first_name', $userData['first_name']);
        $this->db->bind(':last_name', $userData['last_name']);
        $this->db->bind(':phone', $userData['phone']);
        $this->db->bind(':address', $userData['address']);

        if ($this->db->execute()) {
            return ['success' => true, 'user_id' => $this->db->lastInsertId()];
        }
        return ['success' => false, 'errors' => ['Registration failed']];
    }

    public function login($username, $password) {
        $this->db->query('SELECT * FROM users WHERE username = :username OR email = :username');
        $this->db->bind(':username', $username);
        $user = $this->db->single();

        if ($user && password_verify($password, $user->password)) {
            $_SESSION['user_id'] = $user->id;
            $_SESSION['username'] = $user->username;
            $_SESSION['user_role'] = $user->role;
            $_SESSION['user_name'] = $user->first_name . ' ' . $user->last_name;
            $_SESSION['first_name'] = $user->first_name;
            $_SESSION['last_name'] = $user->last_name;
            $_SESSION['email'] = $user->email;
            return true;
        }
        return false;
    }
}
\end{lstlisting}

% Database Design
\section{Database Design}

\subsection{Entity Relationship Diagram}
The database design follows normalized principles with clear relationships between entities:

\begin{figure}[H]
\centering
\begin{tikzpicture}[node distance=4cm]
    \node (users) [rectangle, draw, fill=blue!20, text width=2.5cm, text centered] {Users\\(id, username, email, password, role)};
    \node (events) [rectangle, draw, fill=green!20, text width=2.5cm, text centered, right of=users] {Events\\(id, title, date, price, tickets)};
    \node (bookings) [rectangle, draw, fill=red!20, text width=2.5cm, text centered, below of=users, yshift=1cm] {Bookings\\(id, user\_id, event\_id, quantity)};
    \node (cart) [rectangle, draw, fill=yellow!20, text width=2.5cm, text centered, below of=events, yshift=1cm] {Cart\\(id, user\_id, event\_id, quantity)};
    \node (payments) [rectangle, draw, fill=purple!20, text width=2.5cm, text centered, below of=bookings] {Payments\\(id, booking\_id, amount, status)};

    \draw [->] (users) -- node[above] {1:M} (bookings);
    \draw [->] (events) -- node[above] {1:M} (bookings);
    \draw [->] (users) -- node[above] {1:M} (cart);
    \draw [->] (events) -- node[above] {1:M} (cart);
    \draw [->] (bookings) -- node[left] {1:1} (payments);
\end{tikzpicture}
\caption{Entity Relationship Diagram}
\end{figure}

\subsection{Database Schema}

\subsubsection{Users Table}
Stores user account information and authentication data:

\begin{lstlisting}[language=SQL, caption=Users Table Schema]
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
\end{lstlisting}

\subsubsection{Events Table}
Contains event information and availability data:

\begin{lstlisting}[language=SQL, caption=Events Table Schema]
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    total_tickets INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
\end{lstlisting}

\subsubsection{Bookings Table}
Manages booking transactions and attendee information:

\begin{lstlisting}[language=SQL, caption=Bookings Table Schema]
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);
\end{lstlisting}

\subsubsection{Cart Table}
Temporary storage for items before checkout:

\begin{lstlisting}[language=SQL, caption=Cart Table Schema]
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_event (user_id, event_id)
);
\end{lstlisting}

\subsubsection{Payments Table}
Handles payment processing and transaction records:

\begin{lstlisting}[language=SQL, caption=Payments Table Schema]
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    payment_reference VARCHAR(255) NOT NULL UNIQUE,
    payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF',
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    payment_gateway VARCHAR(50) DEFAULT 'simulation',
    payment_details JSON,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_payment_reference (payment_reference),
    INDEX idx_payment_status (payment_status),
    INDEX idx_booking_id (booking_id)
);
\end{lstlisting}

% Implementation Details
\section{Implementation Details}

\subsection{Booking System Implementation}
The booking system handles the complete workflow from cart management to payment processing:

\begin{lstlisting}[language=PHP, caption=Booking Manager Class]
class BookingManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function createBooking($userId, $eventId, $quantity, $attendeeData) {
        try {
            // Begin transaction
            $this->db->getConnection()->beginTransaction();

            // Check event availability
            $this->db->query('SELECT * FROM events WHERE id = :event_id AND status = "active"');
            $this->db->bind(':event_id', $eventId);
            $event = $this->db->single();

            if (!$event) {
                throw new Exception('Event not found or inactive');
            }

            if ($event->available_tickets < $quantity) {
                throw new Exception('Insufficient tickets available');
            }

            // Calculate total amount
            $totalAmount = $quantity * $event->price;

            // Generate booking reference
            $bookingReference = generateBookingReference();

            // Create booking
            $this->db->query('INSERT INTO bookings (user_id, event_id, quantity, total_amount,
                             booking_reference, attendee_name, attendee_email, attendee_phone,
                             special_requirements) VALUES (:user_id, :event_id, :quantity,
                             :total_amount, :booking_reference, :attendee_name, :attendee_email,
                             :attendee_phone, :special_requirements)');

            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':total_amount', $totalAmount);
            $this->db->bind(':booking_reference', $bookingReference);
            $this->db->bind(':attendee_name', $attendeeData['name']);
            $this->db->bind(':attendee_email', $attendeeData['email']);
            $this->db->bind(':attendee_phone', $attendeeData['phone']);
            $this->db->bind(':special_requirements', $attendeeData['special_requirements']);

            if (!$this->db->execute()) {
                throw new Exception('Failed to create booking');
            }

            $bookingId = $this->db->lastInsertId();

            // Update event availability
            $this->db->query('UPDATE events SET available_tickets = available_tickets - :quantity
                             WHERE id = :event_id');
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':event_id', $eventId);
            $this->db->execute();

            // Commit transaction
            $this->db->getConnection()->commit();

            return $bookingId;

        } catch (Exception $e) {
            // Rollback transaction
            $this->db->getConnection()->rollback();
            throw $e;
        }
    }
}
\end{lstlisting}

\subsection{Cart Management System}
The cart system provides temporary storage for items before checkout:

\begin{lstlisting}[language=PHP, caption=Cart Manager Class]
class CartManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function addToCart($userId, $eventId, $quantity) {
        // Check if item already exists in cart
        $this->db->query('SELECT * FROM cart WHERE user_id = :user_id AND event_id = :event_id');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $existingItem = $this->db->single();

        if ($existingItem) {
            // Update quantity
            $this->db->query('UPDATE cart SET quantity = quantity + :quantity
                             WHERE user_id = :user_id AND event_id = :event_id');
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
        } else {
            // Add new item
            $this->db->query('INSERT INTO cart (user_id, event_id, quantity)
                             VALUES (:user_id, :event_id, :quantity)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':quantity', $quantity);
        }

        return $this->db->execute();
    }

    public function getCartItems($userId) {
        $this->db->query('SELECT c.*, e.title, e.price, e.event_date, e.event_time,
                         e.venue, e.image_url, e.available_tickets
                         FROM cart c
                         JOIN events e ON c.event_id = e.id
                         WHERE c.user_id = :user_id AND e.status = "active"');
        $this->db->bind(':user_id', $userId);
        return $this->db->resultset();
    }

    public function removeFromCart($userId, $eventId) {
        $this->db->query('DELETE FROM cart WHERE user_id = :user_id AND event_id = :event_id');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        return $this->db->execute();
    }

    public function clearCart($userId) {
        $this->db->query('DELETE FROM cart WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }
}
\end{lstlisting}

\subsection{Security Implementation}

\subsubsection{CSRF Protection}
Cross-Site Request Forgery protection is implemented using tokens:

\begin{lstlisting}[language=PHP, caption=CSRF Token Implementation]
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
\end{lstlisting}

\subsubsection{Input Sanitization}
All user inputs are sanitized to prevent XSS attacks:

\begin{lstlisting}[language=PHP, caption=Input Sanitization Function]
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}
\end{lstlisting}

\subsubsection{Session Security}
Session management includes timeout and security measures:

\begin{lstlisting}[language=PHP, caption=Session Security Functions]
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /auth/login.php');
        exit();
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: /index.php');
        exit();
    }
}
\end{lstlisting}

% Deployment Guide
\section{Deployment Guide}

\subsection{Docker Deployment (Recommended)}

\subsubsection{Prerequisites}
\begin{itemize}
    \item Docker Desktop installed and running
    \item Git for repository cloning
    \item Minimum 4GB RAM and 10GB disk space
\end{itemize}

\subsubsection{Deployment Steps}

\begin{lstlisting}[language=bash, caption=Docker Deployment Commands]
# Clone the repository
git clone <repository-url>
cd ukuqala-events

# Start all services
docker-compose up -d --build

# Verify deployment
docker-compose ps

# Access the application
# Web Application: http://localhost:8000
# phpMyAdmin: http://localhost:8081
# MySQL: localhost:3307
\end{lstlisting}

\subsubsection{Docker Configuration}
The docker-compose.yml file defines three services:

\begin{lstlisting}[language=yaml, caption=Docker Compose Configuration]
services:
  web:
    build: .
    container_name: event_booking_web
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./assets/images/events:/var/www/html/assets/images/events
    environment:
      DB_HOST: mysql:3306
      DB_USER: event_user
      DB_PASS: event_password
      DB_NAME: event_booking_system
    depends_on:
      - mysql

  mysql:
    image: mysql:8.0
    container_name: event_booking_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-init-data.sql

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: event_booking_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: event_user
      PMA_PASSWORD: event_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
\end{lstlisting}

\subsection{Manual Deployment}

\subsubsection{System Requirements}
\begin{itemize}
    \item PHP 8.0+ with extensions: mysqli, json, curl, mbstring
    \item MySQL 8.0+ or MariaDB 10.4+
    \item Apache 2.4+ or Nginx 1.18+
    \item Minimum 2GB RAM and 5GB disk space
\end{itemize}

\subsubsection{Installation Steps}

\begin{enumerate}
    \item \textbf{Download and Extract}
    \begin{lstlisting}[language=bash]
# Download source code
wget <download-url>
unzip ukuqala-events.zip
cd ukuqala-events
    \end{lstlisting}

    \item \textbf{Database Setup}
    \begin{lstlisting}[language=bash]
# Create database
mysql -u root -p
CREATE DATABASE event_booking_system;
CREATE USER 'event_user'@'localhost' IDENTIFIED BY 'event_password';
GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'localhost';
FLUSH PRIVILEGES;

# Import schema
mysql -u event_user -p event_booking_system < database/schema.sql
mysql -u event_user -p event_booking_system < database/init-data.sql
    \end{lstlisting}

    \item \textbf{Configuration}
    \begin{lstlisting}[language=PHP]
// Update includes/config.php
define('DB_HOST', 'localhost');
define('DB_USER', 'event_user');
define('DB_PASS', 'event_password');
define('DB_NAME', 'event_booking_system');
define('SITE_URL', 'http://your-domain.com');
    \end{lstlisting}

    \item \textbf{File Permissions}
    \begin{lstlisting}[language=bash]
# Set appropriate permissions
chmod -R 755 /var/www/html/ukuqala-events
chmod -R 777 /var/www/html/ukuqala-events/assets/images/events
    \end{lstlisting}
\end{enumerate}

% User Manual
\section{User Manual}

\subsection{Getting Started}

\subsubsection{System Access}
\begin{itemize}
    \item \textbf{Application URL:} http://localhost:8000
    \item \textbf{Admin Panel:} http://localhost:8000/admin/
    \item \textbf{Database Management:} http://localhost:8081 (phpMyAdmin)
\end{itemize}

\subsubsection{Default Credentials}
\begin{table}[H]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Account Type} & \textbf{Username/Email} & \textbf{Password} \\
\hline
Administrator & <EMAIL> & password \\
Database Root & root & root\_password \\
Database User & event\_user & event\_password \\
\hline
\end{tabular}
\caption{Default System Credentials}
\end{table}

\subsection{User Operations}

\subsubsection{User Registration and Login}
\begin{enumerate}
    \item Navigate to the registration page: \texttt{/auth/register.php}
    \item Fill in the required information:
    \begin{itemize}
        \item Username (unique)
        \item Email address (unique)
        \item Password (minimum 8 characters)
        \item First and Last name
        \item Phone number (optional)
        \item Address (optional)
    \end{itemize}
    \item Submit the form and verify email if required
    \item Login using credentials at \texttt{/auth/login.php}
    \item Access user dashboard at \texttt{/user/dashboard.php}
\end{enumerate}

\subsubsection{Event Browsing and Booking}
\begin{enumerate}
    \item \textbf{Browse Events}
    \begin{itemize}
        \item Visit the events page: \texttt{/events/}
        \item Use search and filter options to find events
        \item View event categories and upcoming events
    \end{itemize}

    \item \textbf{View Event Details}
    \begin{itemize}
        \item Click on an event to view full details
        \item Review event information, pricing, and availability
        \item Check venue location and event schedule
    \end{itemize}

    \item \textbf{Add to Cart}
    \begin{itemize}
        \item Select desired quantity of tickets
        \item Click "Add to Cart" button
        \item Continue shopping or proceed to cart
    \end{itemize}

    \item \textbf{Checkout Process}
    \begin{itemize}
        \item Review cart items at \texttt{/booking/cart.php}
        \item Proceed to checkout at \texttt{/booking/checkout.php}
        \item Fill in attendee information
        \item Select payment method
        \item Confirm booking and complete payment
    \end{itemize}

    \item \textbf{Booking Confirmation}
    \begin{itemize}
        \item Receive booking confirmation with reference number
        \item Get email confirmation (if configured)
        \item Access booking details in user dashboard
    \end{itemize}
\end{enumerate}

\subsubsection{User Dashboard Features}
\begin{itemize}
    \item \textbf{Profile Management:} Update personal information and password
    \item \textbf{Booking History:} View all past and upcoming bookings
    \item \textbf{Booking Details:} Access detailed booking information and status
    \item \textbf{Account Settings:} Manage account preferences and notifications
\end{itemize}

\subsection{Administrative Operations}

\subsubsection{Admin Dashboard Access}
\begin{enumerate}
    \item Login with administrator credentials
    \item Access admin panel at \texttt{/admin/}
    \item Navigate through different management sections
\end{enumerate}

\subsubsection{Event Management}
\begin{enumerate}
    \item \textbf{Create New Event}
    \begin{itemize}
        \item Navigate to Events section in admin panel
        \item Click "Add New Event" button
        \item Fill in event details:
        \begin{itemize}
            \item Title and description
            \item Date, time, and venue information
            \item Pricing and ticket availability
            \item Category and organizer details
            \item Event image upload
        \end{itemize}
        \item Save and publish event
    \end{itemize}

    \item \textbf{Edit Existing Events}
    \begin{itemize}
        \item Select event from events list
        \item Modify event information as needed
        \item Update availability and pricing
        \item Save changes
    \end{itemize}

    \item \textbf{Event Status Management}
    \begin{itemize}
        \item Activate or deactivate events
        \item Cancel events if necessary
        \item Monitor ticket sales and availability
    \end{itemize}
\end{enumerate}

\subsubsection{Booking Management}
\begin{itemize}
    \item \textbf{View All Bookings:} Access comprehensive booking list
    \item \textbf{Booking Details:} Review individual booking information
    \item \textbf{Status Updates:} Confirm or cancel bookings as needed
    \item \textbf{Payment Tracking:} Monitor payment status and processing
\end{itemize}

\subsubsection{User Management}
\begin{itemize}
    \item \textbf{User Accounts:} View and manage user accounts
    \item \textbf{Role Assignment:} Assign admin or user roles
    \item \textbf{Account Status:} Activate or deactivate user accounts
\end{itemize}

\subsubsection{Reports and Analytics}
\begin{itemize}
    \item \textbf{Sales Reports:} Generate revenue and booking reports
    \item \textbf{Event Analytics:} Monitor event performance and attendance
    \item \textbf{User Statistics:} Track user registration and activity
    \item \textbf{System Metrics:} Monitor system usage and performance
\end{itemize}

% Testing and Quality Assurance
\section{Testing and Quality Assurance}

\subsection{Testing Strategy}

\subsubsection{Functional Testing Checklist}
\begin{itemize}
    \item[$\square$] User registration with valid and invalid data
    \item[$\square$] User login and logout functionality
    \item[$\square$] Password reset and recovery process
    \item[$\square$] Event browsing and search functionality
    \item[$\square$] Cart operations (add, update, remove items)
    \item[$\square$] Checkout process and booking creation
    \item[$\square$] Payment processing simulation
    \item[$\square$] Email notification system
    \item[$\square$] Admin panel access and operations
    \item[$\square$] Event management (CRUD operations)
    \item[$\square$] Booking management and status updates
    \item[$\square$] User profile management
    \item[$\square$] Report generation and analytics
\end{itemize}

\subsubsection{Security Testing}
\begin{itemize}
    \item[$\square$] SQL injection prevention testing
    \item[$\square$] Cross-site scripting (XSS) protection
    \item[$\square$] CSRF token validation
    \item[$\square$] Session security and timeout
    \item[$\square$] Password hashing verification
    \item[$\square$] Input validation and sanitization
    \item[$\square$] Access control and authorization
    \item[$\square$] File upload security
\end{itemize}

\subsubsection{Performance Testing}
\begin{itemize}
    \item[$\square$] Database query optimization
    \item[$\square$] Page load time analysis
    \item[$\square$] Concurrent user handling
    \item[$\square$] Memory usage monitoring
    \item[$\square$] Image optimization and loading
\end{itemize}

\subsubsection{Compatibility Testing}
\begin{itemize}
    \item[$\square$] Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
    \item[$\square$] Mobile responsiveness testing
    \item[$\square$] Different screen resolutions
    \item[$\square$] Operating system compatibility
\end{itemize}

% Maintenance and Support
\section{Maintenance and Support}

\subsection{Regular Maintenance Tasks}

\subsubsection{Database Maintenance}
\begin{itemize}
    \item \textbf{Daily Backups}
    \begin{lstlisting}[language=bash]
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u event_user -p event_booking_system > backup_$DATE.sql
    \end{lstlisting}

    \item \textbf{Index Optimization}
    \begin{lstlisting}[language=SQL]
-- Analyze and optimize tables
ANALYZE TABLE users, events, bookings, cart, payments;
OPTIMIZE TABLE users, events, bookings, cart, payments;
    \end{lstlisting}

    \item \textbf{Data Cleanup}
    \begin{lstlisting}[language=SQL]
-- Clean expired sessions
DELETE FROM user_sessions WHERE expires_at < NOW();

-- Clean old cart items (older than 7 days)
DELETE FROM cart WHERE added_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- Clean expired password reset tokens
DELETE FROM password_reset_tokens WHERE expires_at < NOW() OR used = TRUE;
    \end{lstlisting}
\end{itemize}

\subsubsection{System Monitoring}
\begin{itemize}
    \item \textbf{Log File Management}
    \begin{itemize}
        \item Monitor application error logs
        \item Rotate log files to prevent disk space issues
        \item Set up log alerts for critical errors
    \end{itemize}

    \item \textbf{Performance Monitoring}
    \begin{itemize}
        \item Monitor database query performance
        \item Track page load times
        \item Monitor server resource usage
    \end{itemize}

    \item \textbf{Security Monitoring}
    \begin{itemize}
        \item Monitor failed login attempts
        \item Track suspicious user activities
        \item Regular security updates
    \end{itemize}
\end{itemize}

\subsection{Troubleshooting Guide}

\subsubsection{Common Issues and Solutions}

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{6cm}|p{4cm}|}
\hline
\textbf{Issue} & \textbf{Symptoms} & \textbf{Solution} \\
\hline
Database Connection Failed & "Database connection failed" error message & Check config.php settings, verify MySQL service status \\
\hline
Email Not Sending & Booking confirmations not received & Verify SMTP configuration in config.php \\
\hline
Session Timeout Issues & Users logged out unexpectedly & Check session timeout settings, clear browser cache \\
\hline
Image Upload Failures & Event images not uploading & Check file permissions on upload directory \\
\hline
Cart Items Disappearing & Items removed from cart unexpectedly & Check session configuration and cart cleanup settings \\
\hline
Payment Processing Errors & Payment simulation failures & Verify payment gateway configuration \\
\hline
\end{tabular}
\caption{Common Issues and Solutions}
\end{table}

\subsubsection{Diagnostic Commands}

\begin{lstlisting}[language=bash, caption=System Diagnostic Commands]
# Check Docker container status
docker-compose ps

# View application logs
docker-compose logs web

# Check MySQL status
docker-compose logs mysql

# Test database connection
docker exec -it event_booking_mysql mysql -u event_user -p

# Check file permissions
ls -la assets/images/events/

# Monitor system resources
docker stats
\end{lstlisting}

\subsection{Backup and Recovery}

\subsubsection{Backup Strategy}
\begin{enumerate}
    \item \textbf{Database Backups}
    \begin{itemize}
        \item Daily automated backups
        \item Weekly full system backups
        \item Monthly archive backups
    \end{itemize}

    \item \textbf{File System Backups}
    \begin{itemize}
        \item User uploaded images
        \item Configuration files
        \item Custom modifications
    \end{itemize}

    \item \textbf{Backup Storage}
    \begin{itemize}
        \item Local backup storage
        \item Cloud backup solutions
        \item Offsite backup copies
    \end{itemize}
\end{enumerate}

\subsubsection{Recovery Procedures}
\begin{lstlisting}[language=bash, caption=Database Recovery Commands]
# Stop application
docker-compose down

# Restore database from backup
mysql -u event_user -p event_booking_system < backup_YYYYMMDD_HHMMSS.sql

# Restart application
docker-compose up -d

# Verify system functionality
curl http://localhost:8000
\end{lstlisting}

% Future Enhancements
\section{Future Enhancements}

\subsection{Planned Features}

\subsubsection{Payment Integration}
\begin{itemize}
    \item Integration with real payment gateways (Stripe, PayPal, Square)
    \item Mobile money integration for African markets
    \item Cryptocurrency payment options
    \item Installment payment plans
\end{itemize}

\subsubsection{Mobile Application}
\begin{itemize}
    \item Native iOS and Android applications
    \item Push notifications for event updates
    \item Offline ticket viewing capabilities
    \item QR code ticket generation and scanning
\end{itemize}

\subsubsection{Advanced Features}
\begin{itemize}
    \item Multi-language support (English, French, local languages)
    \item Social media integration and sharing
    \item Event recommendation engine
    \item Advanced analytics and reporting
    \item API for third-party integrations
    \item Real-time chat support
\end{itemize}

\subsection{Scalability Considerations}

\subsubsection{Performance Optimization}
\begin{itemize}
    \item Database sharding for large datasets
    \item Content Delivery Network (CDN) integration
    \item Redis caching implementation
    \item Load balancing for high traffic
    \item Database read replicas
\end{itemize}

\subsubsection{Infrastructure Scaling}
\begin{itemize}
    \item Kubernetes deployment for container orchestration
    \item Microservices architecture migration
    \item Auto-scaling based on demand
    \item Multi-region deployment
    \item Disaster recovery planning
\end{itemize}

% Conclusion
\section{Conclusion}

The Ukuqala Events Online Event Booking System represents a comprehensive solution for event management and ticket booking operations. This technical documentation has covered all aspects of the system from initial design through deployment and maintenance.

\subsection{Key Achievements}
\begin{itemize}
    \item Secure and scalable web application architecture
    \item Comprehensive user and administrative functionality
    \item Modern development practices and security implementations
    \item Docker-based deployment for easy setup and maintenance
    \item Detailed documentation for ongoing support and development
\end{itemize}

\subsection{System Benefits}
\begin{itemize}
    \item \textbf{For Users:} Intuitive event discovery and booking experience
    \item \textbf{For Administrators:} Powerful management tools and analytics
    \item \textbf{For Developers:} Well-structured, maintainable codebase
    \item \textbf{For Organizations:} Reliable, secure event management platform
\end{itemize}

\subsection{Next Steps}
\begin{enumerate}
    \item Deploy the system in production environment
    \item Conduct user acceptance testing
    \item Implement monitoring and analytics
    \item Plan for future feature enhancements
    \item Establish maintenance and support procedures
\end{enumerate}

% Appendices
\section{Appendices}

\subsection{Appendix A: Configuration Files}

\subsubsection{Environment Variables}
\begin{lstlisting}[language=bash, caption=Environment Configuration]
# Database Configuration
DB_HOST=mysql:3306
DB_USER=event_user
DB_PASS=event_password
DB_NAME=event_booking_system

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Configuration
SITE_URL=http://localhost:8000
ENVIRONMENT=development
\end{lstlisting}

\subsection{Appendix B: API Reference}

\subsubsection{Authentication Endpoints}
\begin{lstlisting}[language=json, caption=API Endpoints]
POST /api/auth/login
{
    "username": "<EMAIL>",
    "password": "password123"
}

POST /api/auth/register
{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "John",
    "last_name": "Doe"
}
\end{lstlisting}

\subsection{Appendix C: Database Schema Diagram}

% Note: In a real LaTeX document, you would include actual database diagrams here
% using tools like tikz-er2 or including PDF/PNG images

\vspace{2cm}

\begin{center}
\textbf{Document Information}\\[0.5cm]
\begin{tabular}{ll}
Document Version: & 1.0.0 \\
Last Updated: & December 2024 \\
Authors: <AUTHORS>
Review Date: & March 2025 \\
\end{tabular}
\end{center}

\end{document}
